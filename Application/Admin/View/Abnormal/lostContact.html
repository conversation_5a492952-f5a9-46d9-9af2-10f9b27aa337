<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:C('WEB_TITLE')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">
<include file="Common/header"/>
    <div class="wrapper">
        <div class="ibox">
            <div class="ibox-title">
                <h5>失联异常账号</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-12 m-b">
                        <form method="get" action="" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <div class="input-group-addon gray-bg">是否有停机</div>
                                        <select name="is_stop" class="form-control">
                                            <option value="">请选择</option>
                                            <option value="1" <if condition="$filter.is_stop eq 1">selected="selected"</if>>是</option>
                                            <option value="2" <if condition="$filter.is_stop eq 2">selected="selected"</if>>否</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <div class="input-group-addon gray-bg">是否有复机</div>
                                        <select name="is_resume" class="form-control">
                                            <option value="">请选择</option>
                                            <option value="1" <if condition="$filter.is_resume eq 1">selected="selected"</if>>是</option>
                                            <option value="2" <if condition="$filter.is_resume eq 2">selected="selected"</if>>否</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <div class="input-group-addon gray-bg">宽带账号</div>
                                        <input type="text" class="form-control" placeholder="请输入宽带账号查询" name="keyword" value="{{$filter.keyword}}" >
                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:U('lostContact')}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">ID</th>
                            <th class="text-center">宽带账号</th>
                            <th class="text-center">是否有停机</th>
                            <th class="text-center">执行停机</th>
                            <th class="text-center">是否有复机</th>
                            <th class="text-center">创建时间</th>
                            <th class="text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody class="tooltip-fn">
                    <volist name="exceptionRows" id="exceptionRow">
                        <tr>
                            <td class="text-center">{{$exceptionRow.id}}</td>
                            <td class="text-center">{{$exceptionRow.kd_account}}</td>
                            <td class="text-center">
                                <eq name="exceptionRow.is_stop" value="1">
                                    <span class="text-success">是</span>
                                <else />
                                    <span class="text-danger">否</span>
                                </eq>
                            </td>
                            <td class="text-center">
                                <eq name="exceptionRow.is_exec" value="1">
                                    <span class="text-success">已执行</span>
                                <else />
                                    <span class="text-danger">未执行</span>
                                </eq>
                            </td>
                            <td class="text-center">
                                <eq name="exceptionRow.is_resume" value="1">
                                    <span class="text-success">是</span>
                                <else />
                                    <span class="text-danger">否</span>
                                </eq>
                            </td>
                            <td class="text-center">{{$exceptionRow.created_at|date="Y-m-d",###}}</td>
                            <td class="text-center">
                                <if condition="$exceptionRow.is_stop eq 0 and $exceptionRow.is_exec eq 0">
                                    <a href="{{:U('lostContactShutdown',array('id'=>$exceptionRow['id']))}}" class="btn btn-sm btn-warning">停机</a>
                                <elseif condition="$exceptionRow.is_stop eq 1 and $exceptionRow.is_exec eq 0"/>
                                    -
                                <elseif condition="$exceptionRow.is_stop eq 1 and $exceptionRow.is_exec eq 1 and $exceptionRow.is_resume eq 0"/>
                                    <button class="btn btn-sm btn-success button-edit-tel" data-id="{{$exceptionRow.id}}">编辑</button>
                                <else />
                                    -
                                </if>
                            </td>
                        </tr>
                    </volist>
                    </tbody>
                </table>

                <div class="row">
                    <div class="col-sm-12 pager"><include file="Common/page" /></div>
                </div>

            </div>
        </div>
    
        <!--模态窗-->
        <div id="User-Window" class="modal fade">
            <div class="modal-dialog modal-lg modal-scroll">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">选择社区</h4>
                    </div>
                    <div class="modal-body">
                        <div class="row m-b">
                            <div class="col-sm-5 col-xs-8">
                                <input type="text" class="form-control" name="keyword" placeholder="请输入名称">
                            </div>
                            <div class="col-sm-3 col-xs-4">
                                <button type="button" class="btn btn-primary btn-search">查询</button>
                            </div>
                        </div>
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">ID</th>
                                    <th class="text-center">社区名称</th>
                                    <th class="text-center">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
    <script>
    $(function(){
        var flag=true, $User_Window=$('#User-Window');
        // 点击编辑按钮，显示模态窗
        $('.button-edit-tel').on('click',function(){
            //先获取标签绑定id值，然后发起ajax请求，获取用户信息，渲染到模态窗里
            
            $User_Window.modal('show');
        })
    });
    </script>
</body>
</html>
