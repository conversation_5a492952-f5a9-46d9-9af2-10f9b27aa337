<?php

namespace Admin\Controller;

use Common\Controller\AdminController;
use Think\Exception;
use Admin\Service\Log;

class AbnormalController extends AdminController
{
    /**
     * [_initialize description]
     */
    public function _initialize()
    {
        parent::_initialize();
        $this->assign('menu_active', 'abnormal_active');
    }

    /**
     * 失联账号列表
     *
     * @return void
     */
    public function lostContact()
    {
        $page = I('get.p', 0);
        $filter['keyword'] = I('get.keyword', '');
        $filter['is_stop'] = I('get.is_stop', '');
        $filter['is_resume'] = I('get.is_resume', '');

        $where['type'] = 1;
        if (!empty($filter['keyword'])) {
            $where['kd_account'] = $filter['keyword'];
        }
        if (!empty($filter['is_stop'])) {
            $is_stop = $filter['is_stop'];
            if ($filter['is_stop'] == 2) {
                $is_stop = 0;
            }
            $where['is_stop'] = $is_stop;
        }
        if (!empty($filter['is_resume'])) {
            $is_resume = $filter['is_resume'];
            if ($filter['is_resume'] == 2) {
                $is_resume = 0;
            }
            $where['is_resume'] = $is_resume;
        }

        $total = M('user_account_exception')
            ->where($where)
            ->count();
        $pagesize = C('PAGE_SIZE');
        $pageObject = new \Org\Util\Page($total, $pagesize);
        $pages = $pageObject->show();

        $exceptionRows = M('user_account_exception')
            ->where($where)
            ->page($page)
            ->limit($pagesize)
            ->select();

        $this->assign('exceptionRows', $exceptionRows);
        $this->assign('pages', $pages);
        $this->assign('filter', $filter);
        $this->assign('pagesize', $pagesize);
        $this->display();
    }

    /**
     * 失联账号操作停机
     *
     * @return void
     */
    public function lostContactShutdown()
    {
        $id = I('get.id', '');
        if (empty($id)) {
            $this->error("参数错误");
        }
        // 单个操作处理
        $this->_singleNoflowShutdown($id, 1);

        $this->success("操作成功");
    }

    /**
     * 无流量异常账号列表
     *
     * @return void
     */
    public function noflow()
    {
        $page = I('get.p', 0);
        $filter['keyword'] = I('get.keyword', '');
        $filter['is_stop'] = I('get.is_stop', '');
        $filter['is_resume'] = I('get.is_resume', '');

        $where['type'] = 2;
        if (!empty($filter['keyword'])) {
            $where['kd_account'] = $filter['keyword'];
        }
        if (!empty($filter['is_stop'])) {
            $is_stop = $filter['is_stop'];
            if ($filter['is_stop'] == 2) {
                $is_stop = 0;
            }
            $where['is_stop'] = $is_stop;
        }
        if (!empty($filter['is_resume'])) {
            $is_resume = $filter['is_resume'];
            if ($filter['is_resume'] == 2) {
                $is_resume = 0;
            }
            $where['is_resume'] = $is_resume;
        }

        $total = M('user_account_exception')
            ->where($where)
            ->count();
        $pagesize = C('PAGE_SIZE');
        $pageObject = new \Org\Util\Page($total, $pagesize);
        $pages = $pageObject->show();

        $exceptionRows = M('user_account_exception')
            ->where($where)
            ->page($page)
            ->limit($pagesize)
            ->select();

        $this->assign('exceptionRows', $exceptionRows);
        $this->assign('pages', $pages);
        $this->assign('filter', $filter);
        $this->assign('pagesize', $pagesize);
        $this->display();
    }

    /**
     * 无流量账号操作停机
     *
     * @return void
     */
    public function noflowShutdown()
    {
        $is_batch = I('get.is_batch', 0);
        if ($is_batch == 1) {
            $ids = I('post.ids', []);
            if (empty($ids)) {
                $this->error("请选择需要操作的账号");
            }

            // 批量操作处理
            $this->_batchNoflowShutdown($ids, 2);
        } else {
            $id = I('get.id', '');
            if (empty($id)) {
                $this->error("参数错误");
            }

            // 单个操作处理
            $this->_singleNoflowShutdown($id, 2);
        }

        $this->success("操作成功");
    }

    /**
     * 单个账号停机操作
     *
     * @param int $id 账号ID
     * @return void
     */
    private function _singleNoflowShutdown($id, $type)
    {
        if ($type == 1) {
            $desc = '单个失联账号停机操作';
        } else {
            $desc = '单个无流量账号停机操作';
        }
        $dataRow = M('user_account_exception')->where(['id' => $id])->find();
        if (empty($dataRow)) {
            $this->error("没有找到账号信息");
        }
        if ($dataRow['is_stop'] == 1 && $dataRow['is_exec'] == 1) {
            $this->error("请勿重复操作停机");
        }

        // 记录操作前的数据状态
        $infoBefore = json_encode($dataRow, JSON_UNESCAPED_UNICODE);

        $save = [
            'is_stop' => 1,
            'updated_at' => time()
        ];
        $map = [
            'id' => $id,
            'is_stop' => 0,
            'is_exec' => 0
        ];

        $result = M('user_account_exception')->where($map)->save($save);
        if (!$result) {
            $this->error("停机操作失败");
        }

        // 获取操作后的数据状态
        $dataRowAfter = M('user_account_exception')->where(['id' => $id])->find();
        $infoAfter = json_encode($dataRowAfter, JSON_UNESCAPED_UNICODE);

        // 记录详细操作日志
        $this->_logExceptionAccountOperation(
            $id,
            $type,
            $desc,
            $infoBefore,
            $infoAfter,
            $dataRow['kd_account'] ?? ''
        );
    }

    /**
     * 批量账号停机操作
     *
     * @param array $ids 账号ID数组
     * @return void
     */
    private function _batchNoflowShutdown($ids, $type)
    {
        if (!is_array($ids)) {
            $this->error("参数格式错误");
        }

        // 验证所有ID的有效性并记录操作前状态
        $validIds = [];
        $invalidIds = [];
        $alreadyStoppedIds = [];
        $beforeData = [];

        foreach ($ids as $id) {
            if (empty($id) || !is_numeric($id)) {
                $invalidIds[] = $id;
                continue;
            }

            $dataRow = M('user_account_exception')->where(['id' => $id])->find();
            if (empty($dataRow)) {
                $invalidIds[] = $id;
                continue;
            }

            if ($dataRow['is_stop'] == 1 && $dataRow['is_exec'] == 1) {
                $alreadyStoppedIds[] = $id;
                continue;
            }

            $validIds[] = $id;
            $beforeData[$id] = $dataRow; // 记录操作前的数据状态
        }

        // 如果有无效ID，返回错误信息
        if (!empty($invalidIds)) {
            $this->error("以下账号ID无效或不存在：" . implode(',', $invalidIds));
        }

        // 如果所有账号都已停机，返回提示
        if (empty($validIds) && !empty($alreadyStoppedIds)) {
            $this->error("所选账号均已停机，无需重复操作");
        }

        // 如果没有需要操作的账号
        if (empty($validIds)) {
            $this->error("没有需要操作的账号");
        }

        // 批量更新数据库
        $save = [
            'is_stop' => 1,
            'updated_at' => time()
        ];
        $map = [
            'id' => ['in', $validIds],
            'is_stop' => 0,
            'is_exec' => 0
        ];

        $result = M('user_account_exception')->where($map)->save($save);
        if (!$result) {
            $this->error("批量停机操作失败");
        }

        // 获取操作后的数据状态
        $afterData = [];
        $afterRows = M('user_account_exception')->where(['id' => ['in', $validIds]])->select();
        foreach ($afterRows as $row) {
            $afterData[$row['id']] = $row;
        }

        // 记录详细操作日志
        $this->_logBatchExceptionAccountOperation(
            $validIds,
            $alreadyStoppedIds,
            $type,
            '批量无流量账号停机操作',
            $beforeData,
            $afterData
        );
    }

    /**
     * 记录单个异常账号操作日志
     *
     * @param int $accountId 账号ID
     * @param string $operation 操作类型
     * @param string $desc 操作描述
     * @param string $infoBefore 操作前信息
     * @param string $infoAfter 操作后信息
     * @param string $kdAccount 宽带账号
     * @return void
     */
    private function _logExceptionAccountOperation($accountId, $operation, $desc, $infoBefore, $infoAfter, $kdAccount = '')
    {
        try {
            $logData = [
                'account_id' => $accountId,
                'kd_account' => $kdAccount,
                'operation_type' => $operation,
                'desc' => $desc,
                'info_before' => $infoBefore,
                'info_after' => $infoAfter,
                'operator_id' => session(C('USER_AUTH_KEY')) ? session(C('USER_AUTH_KEY')) : '100',
                'operator_name' => session('admin.name') ? session('admin.name') : 'System',
                'operator_ip' => get_client_ip(),
                'created_at' => time()
            ];

            // 记录到异常账号操作日志表
            $result = M('user_account_exception_log')->add($logData);
            if (!$result) {
                // 日志记录失败不影响主要业务流程，但需要记录错误
                \Think\Log::record("异常账号操作日志记录失败: " . json_encode($logData), \Think\Log::ERR);
            }
        } catch (Exception $e) {
            // 异常处理，不影响主要业务流程
            \Think\Log::record("异常账号操作日志记录异常: " . $e->getMessage(), \Think\Log::ERR);
        }
    }

    /**
     * 记录批量异常账号操作日志
     *
     * @param array $successIds 成功操作的ID数组
     * @param array $skippedIds 跳过的ID数组
     * @param string $operation 操作类型
     * @param string $desc 操作描述
     * @param array $beforeData 操作前数据
     * @param array $afterData 操作后数据
     * @return void
     */
    private function _logBatchExceptionAccountOperation($successIds, $skippedIds, $operation, $desc, $beforeData, $afterData)
    {
        try {
            // 记录批量操作汇总日志
            $summaryLogData = [
                'operation_type' => $operation,
                'desc' => $desc,
                'success_count' => count($successIds),
                'success_ids' => implode(',', $successIds),
                'skipped_count' => count($skippedIds),
                'skipped_ids' => implode(',', $skippedIds),
                'total_count' => count($successIds) + count($skippedIds),
                'operator_id' => session(C('USER_AUTH_KEY')) ? session(C('USER_AUTH_KEY')) : '100',
                'operator_name' => session('admin.name') ? session('admin.name') : 'System',
                'operator_ip' => get_client_ip(),
                'created_at' => time()
            ];

            // 记录批量操作汇总日志
            M('user_account_exception_batch_log')->add($summaryLogData);

            // 为每个成功操作的账号记录详细日志
            foreach ($successIds as $id) {
                $infoBefore = isset($beforeData[$id]) ? json_encode($beforeData[$id], JSON_UNESCAPED_UNICODE) : '';
                $infoAfter = isset($afterData[$id]) ? json_encode($afterData[$id], JSON_UNESCAPED_UNICODE) : '';
                $kdAccount = isset($beforeData[$id]['kd_account']) ? $beforeData[$id]['kd_account'] : '';

                $this->_logExceptionAccountOperation(
                    $id,
                    $operation,
                    $desc . ' (批量操作)',
                    $infoBefore,
                    $infoAfter,
                    $kdAccount
                );
            }

            // 为跳过的账号记录日志
            foreach ($skippedIds as $id) {
                if (isset($beforeData[$id])) {
                    $infoBefore = json_encode($beforeData[$id], JSON_UNESCAPED_UNICODE);
                    $kdAccount = $beforeData[$id]['kd_account'] ?? '';

                    $this->_logExceptionAccountOperation(
                        $id,
                        $operation,
                        $desc . ' (已停机，跳过操作)',
                        $infoBefore,
                        $infoBefore, // 跳过的操作前后数据相同
                        $kdAccount
                    );
                }
            }
        } catch (Exception $e) {
            // 异常处理，不影响主要业务流程
            \Think\Log::record("批量异常账号操作日志记录异常: " . $e->getMessage(), \Think\Log::ERR);
        }
    }
}
